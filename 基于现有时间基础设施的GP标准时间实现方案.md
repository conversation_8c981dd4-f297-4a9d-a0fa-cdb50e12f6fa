# 基于现有时间基础设施的GP标准时间实现方案

## 1. 现有时间管理基础设施分析

### 1.1 现有时间调用链路

基于代码库分析，Trusty TEE现有时间管理架构如下：

```
现有时间调用链路：
TA应用层
    ↓ clock_gettime(clockid_t, struct timespec*)
user/base/lib/libc-rctee/time.c
    ↓ __clock_gettime() → rctee_gettime()
    ↓ _rctee_gettime(clock_id, flags, time)
kernel/rctee/lib/rctee/rctee_core/syscall.c
    ↓ sys_gettime(clock_id, flags, time)
    ↓ current_time_ns() / monotonic_time_s()
LK内核时间管理
    ↓ ARM Generic Timer / SysTick Timer
硬件定时器
```

### 1.2 关键现有组件分析

#### **1.2.1 __clock_gettime()函数 (user/base/lib/libc-rctee/time.c)**
```c
int __clock_gettime(clockid_t clock_id, struct timespec* ts) {
    if (ts == NULL) {
        errno = EFAULT;
        return -1;
    }
    int64_t time;
    int rc = rctee_gettime(clock_id, &time);  // 获取纳秒时间戳
    if (rc) {
        errno = EINVAL;
        return -1;
    }
    ts->tv_sec = (time_t)(time / NS_PER_SEC);   // 转换为timespec
    ts->tv_nsec = (long)(time % NS_PER_SEC);
    return 0;
}
```

#### **1.2.2 rctee_gettime()函数**
```c
int rctee_gettime(clockid_t clock_id, int64_t* time) {
    return _rctee_gettime(clock_id, 0, time);  // 系统调用封装
}
```

#### **1.2.3 sys_gettime()系统调用**
```c
long sys_gettime(uint32_t clock_id, uint32_t flags, user_addr_t time) {
#if USE_IMX_MONOTONIC_TIME
    if (clock_id == CLOCK_MONOTONIC) {
        int64_t monotonic_t_64 = (int64_t)monotonic_time_s();
        return copy_to_user(time, &monotonic_t_64, sizeof(int64_t));
    }
#endif
    // return time in nanoseconds
    lk_time_ns_t t = current_time_ns();
    return copy_to_user(time, &t, sizeof(int64_t));
}
```

#### **1.2.4 现有clockid_t定义**
```c
#define CLOCK_REALTIME           0
#define CLOCK_MONOTONIC          1
#define CLOCK_PROCESS_CPUTIME_ID 2
#define CLOCK_THREAD_CPUTIME_ID  3
#define CLOCK_MONOTONIC_RAW      4
#define CLOCK_REALTIME_COARSE    5
#define CLOCK_MONOTONIC_COARSE   6
#define CLOCK_BOOTTIME           7
#define CLOCK_REALTIME_ALARM     8
#define CLOCK_BOOTTIME_ALARM     9
#define CLOCK_SGI_CYCLE         10
#define CLOCK_TAI               11
```

## 2. GP标准时间功能集成设计

### 2.1 clockid_t扩展设计

在现有clockid_t基础上添加GP标准时间类型：

```c
/* 在 kernel/rctee/include/uapi/uapi/time.h 中添加 */
#define CLOCK_REALTIME           0
#define CLOCK_MONOTONIC          1
// ... 现有定义保持不变 ...
#define CLOCK_TAI               11

/* 新增GP标准时间类型 */
#define CLOCK_TEE_SYSTEM        20  /* 对应TEE_GetSystemTime() */
#define CLOCK_TEE_TA_PERSISTENT 21  /* 对应TEE_GetTAPersistentTime() */
#define CLOCK_TEE_REE           22  /* 对应TEE_GetREETime() */
```

### 2.2 时间格式转换设计

#### **2.2.1 TEE_Time与timespec转换**
```c
/* 在 user/base/lib/libutee/include/tee_internal_api.h 中添加 */
typedef struct {
    uint32_t seconds;  /* 自1970年1月1日UTC以来的秒数 */
    uint32_t millis;   /* 毫秒数 (0-999) */
} TEE_Time;

/* 时间格式转换函数 */
static inline void timespec_to_tee_time(const struct timespec* ts, TEE_Time* tee_time) {
    tee_time->seconds = (uint32_t)ts->tv_sec;
    tee_time->millis = (uint32_t)(ts->tv_nsec / 1000000);
}

static inline void tee_time_to_timespec(const TEE_Time* tee_time, struct timespec* ts) {
    ts->tv_sec = (time_t)tee_time->seconds;
    ts->tv_nsec = (long)(tee_time->millis * 1000000);
}

static inline void ns_to_tee_time(int64_t time_ns, TEE_Time* tee_time) {
    tee_time->seconds = (uint32_t)(time_ns / 1000000000LL);
    tee_time->millis = (uint32_t)((time_ns % 1000000000LL) / 1000000LL);
}
```

### 2.3 现有函数扩展方案

#### **2.3.1 扩展sys_gettime()系统调用**

在`kernel/rctee/lib/rctee/rctee_core/syscall.c`中修改现有`sys_gettime()`：

```c
long sys_gettime(uint32_t clock_id, uint32_t flags, user_addr_t time) {
    int64_t time_ns;

    switch (clock_id) {
        /* 现有时钟类型处理保持不变 */
        case CLOCK_MONOTONIC:
#if USE_IMX_MONOTONIC_TIME
            time_ns = (int64_t)monotonic_time_s() * 1000000000LL;
#else
            time_ns = current_time_ns();
#endif
            break;

        case CLOCK_REALTIME:
        case CLOCK_BOOTTIME:
        default:
            time_ns = current_time_ns();
            break;

        /* 新增GP标准时间类型处理 */
        case CLOCK_TEE_SYSTEM:
            return sys_gettime_tee_system(flags, time);

        case CLOCK_TEE_TA_PERSISTENT:
            return sys_gettime_tee_ta_persistent(flags, time);

        case CLOCK_TEE_REE:
            return sys_gettime_tee_ree(flags, time);
    }

    return copy_to_user(time, &time_ns, sizeof(int64_t));
}
```

#### **2.3.2 新增GP时间处理函数**

在同一文件中添加GP时间处理函数：

```c
/* GP标准系统时间获取 */
static long sys_gettime_tee_system(uint32_t flags, user_addr_t time) {
    int64_t time_ns = current_time_ns();

    /* 转换为Unix时间戳（需要加上启动时偏移量） */
    static int64_t boot_time_offset = 1640995200000000000LL; /* 示例偏移量 */
    time_ns += boot_time_offset;

    return copy_to_user(time, &time_ns, sizeof(int64_t));
}

/* GP标准REE时间获取 */
static long sys_gettime_tee_ree(uint32_t flags, user_addr_t time) {
    /* 简化实现：返回系统时间 */
    /* 实际实现中可通过SMC调用获取REE时间 */
    return sys_gettime_tee_system(flags, time);
}

/* GP标准TA持久时间获取 */
static long sys_gettime_tee_ta_persistent(uint32_t flags, user_addr_t time) {
    TEE_Time tee_time;
    TEE_Result res;
    int64_t time_ns;

    /* 调用TA持久时间获取逻辑 */
    res = get_ta_persistent_time_internal(&tee_time);

    switch (res) {
        case TEE_SUCCESS:
            /* 转换TEE_Time为纳秒时间戳 */
            time_ns = (int64_t)tee_time.seconds * 1000000000LL +
                     (int64_t)tee_time.millis * 1000000LL;
            return copy_to_user(time, &time_ns, sizeof(int64_t));

        case TEE_ERROR_TIME_NOT_SET:
            return -ENODATA;  /* 映射为POSIX错误码 */

        case TEE_ERROR_TIME_NEEDS_RESET:
            return -ESTALE;   /* 映射为POSIX错误码 */

        default:
            return -EIO;
    }
}
```

#### **2.3.3 扩展__clock_gettime()函数**

在`user/base/lib/libc-rctee/time.c`中扩展现有函数：

```c
int __clock_gettime(clockid_t clock_id, struct timespec* ts) {
    if (ts == NULL) {
        errno = EFAULT;
        return -1;
    }

    /* 处理GP标准时间类型 */
    if (clock_id >= CLOCK_TEE_SYSTEM && clock_id <= CLOCK_TEE_REE) {
        return __clock_gettime_gp(clock_id, ts);
    }

    /* 现有逻辑保持不变 */
    int64_t time;
    int rc = rctee_gettime(clock_id, &time);
    if (rc) {
        errno = EINVAL;
        return -1;
    }
    ts->tv_sec = (time_t)(time / NS_PER_SEC);
    ts->tv_nsec = (long)(time % NS_PER_SEC);
    return 0;
}

/* 新增GP时间处理函数 */
static int __clock_gettime_gp(clockid_t clock_id, struct timespec* ts) {
    int64_t time_ns;
    int rc = rctee_gettime(clock_id, &time_ns);

    if (rc) {
        /* 处理GP特定错误码 */
        switch (-rc) {
            case ENODATA:  /* TIME_NOT_SET */
                errno = ENODATA;
                break;
            case ESTALE:   /* TIME_NEEDS_RESET */
                errno = ESTALE;
                break;
            default:
                errno = EINVAL;
                break;
        }
        return -1;
    }

    ts->tv_sec = (time_t)(time_ns / NS_PER_SEC);
    ts->tv_nsec = (long)(time_ns % NS_PER_SEC);
    return 0;
}
```

## 3. TA持久时间偏移量机制实现

### 3.1 偏移量存储结构

```c
/* 在 kernel/rctee/lib/rctee/rctee_core/syscall.c 中添加 */
typedef struct {
    TEE_Time offset;     /* 偏移量值 */
    bool positive;       /* 偏移量符号 */
    uint32_t magic;      /* 魔数 0x54414F46 */
    uint32_t checksum;   /* 校验和 */
} ta_time_offset_t;

/* TA时间上下文缓存 */
static struct {
    char ta_uuid[64];
    ta_time_offset_t offset;
    bool is_valid;
} ta_time_cache = {0};
```

### 3.2 TA持久时间核心实现

```c
/* 获取TA持久时间的内部实现 */
static TEE_Result get_ta_persistent_time_internal(TEE_Time* time) {
    char ta_uuid[64];
    ta_time_offset_t offset;
    TEE_Time sys_time;
    TEE_Result res;

    /* 获取当前TA UUID */
    res = get_current_ta_uuid(ta_uuid, sizeof(ta_uuid));
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 加载偏移量 */
    res = load_ta_time_offset(ta_uuid, &offset);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 获取系统时间 */
    int64_t sys_time_ns = current_time_ns();
    ns_to_tee_time(sys_time_ns, &sys_time);

    /* 计算TA持久时间 */
    return calculate_ta_time(&sys_time, &offset, time);
}

/* 设置TA持久时间的内部实现 */
static TEE_Result set_ta_persistent_time_internal(const TEE_Time* time) {
    char ta_uuid[64];
    ta_time_offset_t offset;
    TEE_Time sys_time;
    TEE_Result res;

    /* 获取当前TA UUID */
    res = get_current_ta_uuid(ta_uuid, sizeof(ta_uuid));
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 获取系统时间 */
    int64_t sys_time_ns = current_time_ns();
    ns_to_tee_time(sys_time_ns, &sys_time);

    /* 计算偏移量 */
    res = calculate_offset(time, &sys_time, &offset);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 保存偏移量 */
    return save_ta_time_offset(ta_uuid, &offset);
}
```

## 4. GP API层实现

### 4.1 GP时间API实现 (user/base/lib/libutee/tee_time.c)

```c
#include <tee_internal_api.h>
#include <time.h>
#include <errno.h>

/*
 * TEE_GetSystemTime - 获取系统时间
 * 复用现有__clock_gettime()机制
 */
void TEE_GetSystemTime(TEE_Time* time) {
    if (!time) {
        panic("TEE_GetSystemTime: null pointer");
    }

    struct timespec ts;
    int rc = __clock_gettime(CLOCK_TEE_SYSTEM, &ts);
    if (rc != 0) {
        panic("TEE_GetSystemTime: failed to get system time");
    }

    timespec_to_tee_time(&ts, time);
}

/*
 * TEE_GetTAPersistentTime - 获取TA持久时间
 * 复用现有__clock_gettime()机制，处理GP特定错误
 */
TEE_Result TEE_GetTAPersistentTime(TEE_Time* time) {
    if (!time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    struct timespec ts;
    int rc = __clock_gettime(CLOCK_TEE_TA_PERSISTENT, &ts);

    if (rc == 0) {
        timespec_to_tee_time(&ts, time);
        return TEE_SUCCESS;
    }

    /* 处理错误情况 */
    memset(time, 0, sizeof(TEE_Time));
    switch (errno) {
        case ENODATA:
            return TEE_ERROR_TIME_NOT_SET;
        case ESTALE:
            return TEE_ERROR_TIME_NEEDS_RESET;
        default:
            return TEE_ERROR_GENERIC;
    }
}

/*
 * TEE_SetTAPersistentTime - 设置TA持久时间
 * 需要新增专门的系统调用
 */
TEE_Result TEE_SetTAPersistentTime(const TEE_Time* time) {
    if (!time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 验证时间参数 */
    if (time->millis >= 1000) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 调用专门的设置系统调用 */
    int rc = _rctee_set_ta_persistent_time(time);
    return (rc == 0) ? TEE_SUCCESS : TEE_ERROR_GENERIC;
}

/*
 * TEE_GetREETime - 获取REE时间
 * 复用现有__clock_gettime()机制
 */
void TEE_GetREETime(TEE_Time* time) {
    if (!time) {
        panic("TEE_GetREETime: null pointer");
    }

    struct timespec ts;
    int rc = __clock_gettime(CLOCK_TEE_REE, &ts);
    if (rc != 0) {
        panic("TEE_GetREETime: failed to get REE time");
    }

    timespec_to_tee_time(&ts, time);
}

/*
 * TEE_Wait - 时间等待
 * 直接复用现有rctee_nanosleep()机制
 */
TEE_Result TEE_Wait(uint32_t timeout) {
    if (timeout == TEE_TIMEOUT_INFINITE) {
        timeout = UINT32_MAX - 1;
    }

    /* 转换毫秒到纳秒 */
    uint64_t sleep_ns = (uint64_t)timeout * 1000000ULL;

    /* 直接调用现有Trusty睡眠API */
    int rc = rctee_nanosleep(CLOCK_MONOTONIC, 0, sleep_ns);
    return (rc == 0) ? TEE_SUCCESS : TEE_ERROR_GENERIC;
}
```

### 4.2 新增系统调用支持

#### **4.2.1 新增TA持久时间设置系统调用**

在`kernel/rctee/lib/rctee/rctee_core/syscall.c`中添加：

```c
/* 新增TA持久时间设置系统调用 */
long sys_set_ta_persistent_time(user_addr_t time) {
    TEE_Time tee_time;
    TEE_Result res;

    /* 从用户空间复制时间数据 */
    int rc = copy_from_user(&tee_time, time, sizeof(TEE_Time));
    if (rc < 0) {
        return rc;
    }

    /* 调用内部设置函数 */
    res = set_ta_persistent_time_internal(&tee_time);
    return (res == TEE_SUCCESS) ? 0 : -EIO;
}
```

#### **4.2.2 用户空间系统调用封装**

在`user/base/lib/libc-rctee/time.c`中添加：

```c
/* 新增TA持久时间设置接口 */
int _rctee_set_ta_persistent_time(const TEE_Time* time) {
    return _rctee_syscall(__NR_set_ta_persistent_time, (user_addr_t)time);
}
```

## 5. 文件修改清单

### 5.1 需要修改的现有文件

#### **5.1.1 kernel/rctee/include/uapi/uapi/time.h** (新增3行)
```c
/* 在现有CLOCK_TAI定义后添加 */
#define CLOCK_TEE_SYSTEM        20
#define CLOCK_TEE_TA_PERSISTENT 21
#define CLOCK_TEE_REE           22
```

#### **5.1.2 user/base/lib/libc-rctee/time.c** (修改约30行)
```c
/* 修改__clock_gettime()函数，添加GP时间类型处理 */
/* 新增__clock_gettime_gp()静态函数 */
/* 新增_rctee_set_ta_persistent_time()函数 */
```

#### **5.1.3 kernel/rctee/lib/rctee/rctee_core/syscall.c** (修改约80行)
```c
/* 修改sys_gettime()函数，添加GP时间类型处理 */
/* 新增sys_gettime_tee_*()静态函数 */
/* 新增sys_set_ta_persistent_time()函数 */
/* 新增TA持久时间管理相关内部函数 */
```

#### **5.1.4 user/base/lib/libutee/include/tee_internal_api.h** (新增约40行)
```c
/* 新增TEE_Time结构体定义 */
/* 新增GP错误码定义 */
/* 新增时间运算宏定义 */
/* 新增GP时间API函数声明 */
/* 新增时间格式转换内联函数 */
```

### 5.2 需要新增的文件

#### **5.2.1 user/base/lib/libutee/tee_time.c** (新增约150行)
```c
/* 实现所有5个GP时间API */
/* 基于现有时间基础设施的封装实现 */
```

## 6. 实现优势

### 6.1 最大化复用现有基础设施

1. **复用现有时间调用链路**：完全基于`__clock_gettime()` → `rctee_gettime()` → `sys_gettime()`
2. **复用现有clockid_t机制**：通过扩展clockid_t值支持GP时间类型
3. **复用现有错误处理**：利用errno机制处理GP特定错误
4. **复用现有睡眠机制**：TEE_Wait直接使用`rctee_nanosleep()`

### 6.2 最小化代码修改

- **总代码量**：约300行（含注释）
- **新增文件**：仅1个（tee_time.c）
- **修改文件**：仅4个现有文件的小幅修改
- **新增系统调用**：仅1个（set_ta_persistent_time）

### 6.3 完全兼容现有架构

- 不破坏任何现有时间API功能
- clockid_t扩展向后兼容
- 系统调用接口保持兼容
- 错误处理机制统一

### 6.4 保持核心设计特性

- 继续使用OP-TEE偏移量机制
- 完整的GP标准错误处理
- 时间运算宏和数据结构设计
- 原子性存储保证

这个基于现有时间基础设施的重新设计方案，既最大化复用了Trusty TEE现有的成熟时间管理组件，又保持了GP标准的完整支持，是一个更加简洁和兼容的实现方案。
