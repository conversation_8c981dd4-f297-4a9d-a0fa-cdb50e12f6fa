# GP标准时间功能简化适配方案

## 1. 设计目标

参考OP-TEE的实现方式，在Trusty TEE现有系统调用基础上进行简单修改，实现GP标准时间功能：
- TEE_GetSystemTime - 获取系统时间
- TEE_GetTAPersistentTime - 获取TA持久时间
- TEE_SetTAPersistentTime - 设置TA持久时间
- TEE_GetREETime - 获取REE时间
- TEE_Wait - 时间等待

## 2. 设计原则

- **简化优先**：最小化修改，复用现有系统调用框架
- **参考OP-TEE**：借鉴OP-TEE的成熟实现方案
- **GP标准兼容**：满足GP规范的基本要求
- **渐进式实现**：先实现基础功能，后续可扩展

## 3. 现有系统调用分析

基于代码库分析，Trusty TEE现有时间相关系统调用：

<augment_code_snippet path="kernel/rctee/lib/rctee/rctee_core/syscall.c" mode="EXCERPT">
```c
long sys_gettime(uint32_t clock_id, uint32_t flags, user_addr_t time) {
    lk_time_ns_t t = current_time_ns();
    return copy_to_user(time, &t, sizeof(int64_t));
}

long sys_nanosleep(uint32_t clock_id, uint32_t flags, uint64_t sleep_time) {
    thread_sleep_ns(sleep_time);
    return NO_ERROR;
}
```
</augment_code_snippet>

**现有优势：**
- 已有基础的时间获取和睡眠系统调用
- 支持纳秒级精度的时间操作
- 完善的存储服务支持原子操作

**需要简单扩展：**
- 添加GP标准的时间格式转换
- 增加TA持久时间的存储和状态管理
- 添加时间回滚检测

## 4. 简化实现方案

### 4.1 GP标准数据结构

```c
// GP标准时间结构体
typedef struct {
    uint32_t seconds;  // 自1970年1月1日UTC以来的秒数
    uint32_t millis;   // 毫秒数 (0-999)
} TEE_Time;

// TA持久时间状态
typedef enum {
    TA_TIME_NOT_SET = 0,      // 未设置
    TA_TIME_SUCCESS = 1,      // 正常
    TA_TIME_NEEDS_RESET = 2   // 需要重置
} ta_time_state_t;

// TA持久时间存储结构
typedef struct {
    uint32_t magic;           // 魔数 0x54415449
    TEE_Time persistent_time; // 持久时间
    uint64_t rtc_reference;   // RTC参考值
    ta_time_state_t state;    // 状态
    uint32_t checksum;        // 校验和
} ta_time_storage_t;
```

### 4.2 系统调用扩展

参考OP-TEE实现，在现有sys_gettime基础上扩展：

```c
// 扩展时间类型定义
#define TIME_CAT_SYSTEM        0  // 系统时间
#define TIME_CAT_TA_PERSISTENT 1  // TA持久时间
#define TIME_CAT_REE          2  // REE时间

// 扩展现有sys_gettime支持GP格式
long sys_gettime(uint32_t time_cat, uint32_t flags, user_addr_t time) {
    TEE_Time tee_time;
    int rc;

    switch (time_cat) {
        case TIME_CAT_SYSTEM:
            rc = get_system_time(&tee_time);
            break;
        case TIME_CAT_TA_PERSISTENT:
            rc = get_ta_persistent_time(&tee_time);
            break;
        case TIME_CAT_REE:
            rc = get_ree_time(&tee_time);
            break;
        default:
            return ERR_INVALID_ARGS;
    }

    if (rc < 0) return rc;
    return copy_to_user(time, &tee_time, sizeof(TEE_Time));
}

// 新增TA持久时间设置系统调用
long sys_set_ta_persistent_time(user_addr_t time) {
    TEE_Time tee_time;
    int rc = copy_from_user(&tee_time, time, sizeof(TEE_Time));
    if (rc < 0) return rc;

    return set_ta_persistent_time(&tee_time);
}
```

### 4.3 核心实现函数

```c
// 获取系统时间 - 基于current_time_ns()
static int get_system_time(TEE_Time* time) {
    lk_time_ns_t current_ns = current_time_ns();

    // 转换为Unix时间戳（需要加上系统启动时的Unix时间）
    uint64_t unix_time_ns = current_ns + get_boot_time_offset();

    time->seconds = (uint32_t)(unix_time_ns / 1000000000ULL);
    time->millis = (uint32_t)((unix_time_ns % 1000000000ULL) / 1000000ULL);

    return 0;
}

// 获取REE时间 - 通过现有机制
static int get_ree_time(TEE_Time* time) {
    // 可以通过SMC调用获取REE时间，或返回系统时间
    return get_system_time(time);
}

// 获取TA持久时间
static int get_ta_persistent_time(TEE_Time* time) {
    char ta_uuid[64];
    ta_time_storage_t storage_data;

    // 获取当前TA的UUID
    get_current_ta_uuid(ta_uuid, sizeof(ta_uuid));

    // 从存储读取TA时间数据
    int rc = load_ta_time_storage(ta_uuid, &storage_data);
    if (rc < 0) {
        return TEE_ERROR_TIME_NOT_SET;
    }

    // 检查时间回滚
    if (check_time_rollback(&storage_data)) {
        return TEE_ERROR_TIME_NEEDS_RESET;
    }

    *time = storage_data.persistent_time;
    return TEE_SUCCESS;
}

// 设置TA持久时间
static int set_ta_persistent_time(const TEE_Time* time) {
    char ta_uuid[64];
    ta_time_storage_t storage_data;

    get_current_ta_uuid(ta_uuid, sizeof(ta_uuid));

    // 构造存储数据
    storage_data.magic = 0x54415449;
    storage_data.persistent_time = *time;
    storage_data.rtc_reference = current_time_ns();
    storage_data.state = TA_TIME_SUCCESS;
    storage_data.checksum = calculate_checksum(&storage_data);

    // 原子性写入存储
    return save_ta_time_storage(ta_uuid, &storage_data);
}
```

### 4.4 GP API层实现

在user/base/lib/libutee/中添加tee_time.c：

```c
// TEE_GetSystemTime实现
void TEE_GetSystemTime(TEE_Time* time) {
    if (!time) {
        TEE_Panic(0);  // GP标准要求panic
    }

    long rc = syscall(__NR_gettime, TIME_CAT_SYSTEM, 0, (user_addr_t)time);
    if (rc < 0) {
        TEE_Panic(0);
    }
}

// TEE_GetTAPersistentTime实现
TEE_Result TEE_GetTAPersistentTime(TEE_Time* time) {
    if (!time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    long rc = syscall(__NR_gettime, TIME_CAT_TA_PERSISTENT, 0, (user_addr_t)time);

    // 转换系统调用返回值为GP错误码
    switch (rc) {
        case 0: return TEE_SUCCESS;
        case -TEE_ERROR_TIME_NOT_SET: return TEE_ERROR_TIME_NOT_SET;
        case -TEE_ERROR_TIME_NEEDS_RESET: return TEE_ERROR_TIME_NEEDS_RESET;
        default: return TEE_ERROR_GENERIC;
    }
}

// TEE_SetTAPersistentTime实现
TEE_Result TEE_SetTAPersistentTime(const TEE_Time* time) {
    if (!time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    long rc = syscall(__NR_set_ta_persistent_time, (user_addr_t)time);
    return (rc == 0) ? TEE_SUCCESS : TEE_ERROR_GENERIC;
}

// TEE_GetREETime实现
void TEE_GetREETime(TEE_Time* time) {
    if (!time) {
        TEE_Panic(0);
    }

    long rc = syscall(__NR_gettime, TIME_CAT_REE, 0, (user_addr_t)time);
    if (rc < 0) {
        TEE_Panic(0);
    }
}

// TEE_Wait实现 - 基于现有sys_nanosleep
TEE_Result TEE_Wait(uint32_t timeout) {
    if (timeout == TEE_TIMEOUT_INFINITE) {
        timeout = UINT32_MAX;
    }

    uint64_t sleep_ns = (uint64_t)timeout * 1000000ULL;
    long rc = syscall(__NR_nanosleep, CLOCK_MONOTONIC, 0, sleep_ns);

    return (rc == 0) ? TEE_SUCCESS : TEE_ERROR_GENERIC;
}
```

## 4. 数据结构设计

### 4.1 GP标准数据结构

```c
// GP标准时间结构体
typedef struct {
    uint32_t seconds;  // 自1970年1月1日UTC以来的秒数
    uint32_t millis;   // 毫秒数 (0-999)
} TEE_Time;

// GP标准错误码
#define TEE_SUCCESS                    0x00000000
#define TEE_ERROR_TIME_NOT_SET         0xFFFF5000
#define TEE_ERROR_TIME_NEEDS_RESET     0xFFFF5001
#define TEE_ERROR_OVERFLOW             0xFFFF300F
#define TEE_ERROR_OUT_OF_MEMORY        0xFFFF000C
#define TEE_ERROR_STORAGE_NO_SPACE     0xFFFF3041
#define TEE_ERROR_CANCEL               0xFFFF0002

// 超时常量
#define TEE_TIMEOUT_INFINITE           0xFFFFFFFF
```

### 4.2 TA持久时间管理数据结构

```c
// TA持久时间状态
typedef enum {
    TA_TIME_NOT_SET = TEE_ERROR_TIME_NOT_SET,      // 未设置
    TA_TIME_SUCCESS = TEE_SUCCESS,                  // 正常
    TA_TIME_NEEDS_RESET = TEE_ERROR_TIME_NEEDS_RESET // 需要重置
} ta_time_state_t;

// TA时间上下文
typedef struct {
    TEE_Time persistent_time;    // 持久时间
    ta_time_state_t state;       // 当前状态
    uint32_t protection_level;   // 保护级别 (100/1000)
    uint64_t rtc_reference;      // RTC参考值
    uint64_t last_system_time;   // 上次系统时间
    bool is_initialized;         // 初始化标志
    // 为未来扩展预留
    void* reserved[4];
} ta_time_context_t;

// 可信存储中的TA时间数据
typedef struct {
    uint32_t magic;              // 魔数 0x54415449 ("TATI")
    uint32_t version;            // 版本号
    TEE_Time persistent_time;    // 持久时间
    uint64_t rtc_reference;      // RTC参考值
    uint32_t checksum;           // CRC32校验和
    // 为未来扩展预留
    uint8_t reserved[32];
} ta_persistent_time_storage_t;
```

### 4.3 时间保护级别数据结构

```c
// 时间保护级别配置
typedef struct {
    uint32_t system_time_protection_level;     // 系统时间保护级别
    uint32_t ta_persistent_time_protection_level; // TA持久时间保护级别
    bool has_secure_timer;                     // 是否有安全定时器
    bool has_ree_timer;                        // 是否有REE定时器
    // 为属性系统集成预留
    char* property_names[8];                   // 属性名称数组
    void* property_handlers[8];                // 属性处理器数组
} time_protection_config_t;
```

### 4.4 时间回滚检测数据结构

```c
// 时间回滚检测上下文
typedef struct {
    uint64_t last_rtc_value;     // 上次RTC值
    uint64_t last_system_time;   // 上次系统时间
    uint32_t rollback_count;     // 回滚次数
    bool rollback_detected;      // 回滚检测标志
    // 为错误处理系统预留
    void* error_handler;         // 错误处理器
    uint32_t error_codes[4];     // 错误码数组
} time_rollback_context_t;
```

## 5. 存储和回滚检测

### 5.1 TA时间存储实现

利用现有存储服务，简化实现：

```c
// TA时间存储文件路径格式
#define TA_TIME_STORAGE_PATH "/ta_time/%s.dat"

// 加载TA时间数据
static int load_ta_time_storage(const char* ta_uuid, ta_time_storage_t* data) {
    char path[128];
    snprintf(path, sizeof(path), TA_TIME_STORAGE_PATH, ta_uuid);

    storage_session_t session;
    file_handle_t file;

    int rc = storage_open_session(&session, STORAGE_CLIENT_TD_PORT);
    if (rc < 0) return rc;

    rc = storage_open_file(session, &file, path, 0, 0);
    if (rc < 0) {
        storage_close_session(session);
        return rc;
    }

    rc = storage_read(file, 0, data, sizeof(ta_time_storage_t));

    storage_close_file(file);
    storage_close_session(session);

    // 验证魔数和校验和
    if (data->magic != 0x54415449 ||
        !verify_checksum(data)) {
        return -1;
    }

    return (rc == sizeof(ta_time_storage_t)) ? 0 : -1;
}

// 保存TA时间数据
static int save_ta_time_storage(const char* ta_uuid, const ta_time_storage_t* data) {
    char path[128];
    snprintf(path, sizeof(path), TA_TIME_STORAGE_PATH, ta_uuid);

    storage_session_t session;
    file_handle_t file;

    int rc = storage_open_session(&session, STORAGE_CLIENT_TD_PORT);
    if (rc < 0) return rc;

    rc = storage_open_file(session, &file, path,
                          STORAGE_FILE_OPEN_CREATE, 0);
    if (rc < 0) {
        storage_close_session(session);
        return rc;
    }

    // 原子性写入
    rc = storage_write(file, 0, data, sizeof(ta_time_storage_t),
                      STORAGE_OP_COMPLETE);

    storage_close_file(file);
    storage_close_session(session);

    return (rc == sizeof(ta_time_storage_t)) ? 0 : -1;
}
```

### 5.2 时间回滚检测

简化的回滚检测实现：

```c
// 检查时间回滚
static bool check_time_rollback(const ta_time_storage_t* storage_data) {
    uint64_t current_rtc = current_time_ns();

    // 如果当前RTC时间小于存储的参考时间，可能发生了回滚
    if (current_rtc < storage_data->rtc_reference) {
        return true;
    }

    // 可以添加更复杂的检测逻辑
    return false;
}

// 计算校验和
static uint32_t calculate_checksum(const ta_time_storage_t* data) {
    // 简单的CRC32实现或者XOR校验
    uint32_t checksum = 0;
    const uint8_t* ptr = (const uint8_t*)data;
    size_t len = sizeof(ta_time_storage_t) - sizeof(uint32_t);

    for (size_t i = 0; i < len; i++) {
        checksum ^= ptr[i];
    }

    return checksum;
}

// 验证校验和
static bool verify_checksum(const ta_time_storage_t* data) {
    uint32_t calculated = calculate_checksum(data);
    return calculated == data->checksum;
}
```

## 6. 系统调用表修改

在kernel/rctee/lib/rctee/include/syscall_table.h中添加：

```c
// 修改现有的gettime系统调用支持时间类型参数
DEF_SYSCALL(0x20, gettime, long, 3, uint32_t time_cat, uint32_t flags, user_addr_t time)

// 新增TA持久时间设置系统调用
DEF_SYSCALL(0x60, set_ta_persistent_time, long, 1, user_addr_t time)
```

## 7. 属性系统集成

为支持GP标准的保护级别查询，简化实现：

```c
// 在user/base/lib/libutee/tee_api_property.c中添加
static const struct user_ta_property time_properties[] = {
    {
        .name = "gpd.tee.systemTime.protectionLevel",
        .type = USER_TA_PROP_TYPE_U32,
        .value.u32 = 1000,  // TEE控制的安全定时器
    },
    {
        .name = "gpd.tee.TAPersistentTime.protectionLevel",
        .type = USER_TA_PROP_TYPE_U32,
        .value.u32 = 1000,  // TEE控制的RTC和可信存储
    },
};

// 注册时间属性
void register_time_properties(void) {
    // 将time_properties添加到全局属性列表
}
```

## 8. 实施步骤

### 8.1 第一步：修改系统调用（1周）

1. **修改sys_gettime**：
   - 在kernel/rctee/lib/rctee/rctee_core/syscall.c中修改现有sys_gettime
   - 添加时间类型参数支持TIME_CAT_SYSTEM、TIME_CAT_TA_PERSISTENT、TIME_CAT_REE

2. **添加sys_set_ta_persistent_time**：
   - 新增TA持久时间设置系统调用
   - 在syscall_table.h中注册新系统调用

### 8.2 第二步：实现核心时间函数（1周）

1. **实现get_system_time**：
   - 基于current_time_ns()转换为GP格式
   - 处理Unix时间戳转换

2. **实现TA持久时间管理**：
   - get_ta_persistent_time和set_ta_persistent_time
   - 集成存储服务和回滚检测

### 8.3 第三步：实现GP API层（1周）

1. **在user/base/lib/libutee/中添加tee_time.c**：
   - 实现TEE_GetSystemTime、TEE_GetTAPersistentTime等API
   - 处理GP标准的错误码转换

2. **更新头文件**：
   - 添加TEE_Time结构体定义
   - 添加GP标准错误码定义

### 8.4 第四步：集成属性系统（0.5周）

1. **添加时间保护级别属性**：
   - 在tee_api_property.c中添加时间相关属性
   - 支持gpd.tee.systemTime.protectionLevel查询

### 8.5 第五步：测试验证（0.5周）

1. **功能测试**：
   - 验证各个GP时间API的基本功能
   - 测试TA持久时间的存储和恢复

2. **回滚检测测试**：
   - 验证时间回滚检测机制
   - 测试错误状态处理

## 9. 文件修改清单

### 9.1 需要修改的文件

```
kernel/rctee/lib/rctee/rctee_core/syscall.c
├── 修改sys_gettime函数
└── 添加sys_set_ta_persistent_time函数

kernel/rctee/lib/rctee/include/syscall_table.h
└── 添加新系统调用定义

user/base/lib/libutee/tee_time.c (新增)
├── TEE_GetSystemTime实现
├── TEE_GetTAPersistentTime实现
├── TEE_SetTAPersistentTime实现
├── TEE_GetREETime实现
└── TEE_Wait实现

user/base/lib/libutee/include/tee_internal_api.h
├── 添加TEE_Time结构体
├── 添加GP错误码定义
└── 添加时间API函数声明

user/base/lib/libutee/tee_api_property.c
└── 添加时间保护级别属性
```

### 9.2 预计工作量

- **总工作量**：约4周
- **核心开发**：3周
- **测试验证**：1周
- **风险缓冲**：建议预留1周

## 10. 总结

### 10.1 简化设计的优势

1. **最小化修改**：主要基于现有系统调用框架扩展
2. **复用现有组件**：充分利用存储服务、定时器等成熟组件
3. **参考OP-TEE**：借鉴成熟的开源实现经验
4. **渐进式实现**：可以分步骤实施，降低风险

### 10.2 关键技术点

1. **时间格式转换**：current_time_ns()到TEE_Time的转换
2. **TA持久时间存储**：利用现有存储服务的原子性操作
3. **回滚检测**：基于RTC参考值的简单检测机制
4. **错误处理**：GP标准错误码的正确映射

### 10.3 后续扩展

实现基础功能后，可以根据需要逐步添加：
- 更复杂的时间回滚检测算法
- TEE_Wait的取消机制
- 更多的时间属性支持
- 性能优化和错误处理完善

这个简化方案在保证GP标准兼容性的同时，最大程度地复用了Trusty TEE现有的基础设施，实现成本低，风险可控。
