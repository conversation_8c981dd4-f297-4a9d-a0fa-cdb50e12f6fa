# Trusty TEE GP标准时间API实施清单

## 📋 实施概览

基于OP-TEE偏移量机制分析，为Trusty TEE设计的GP标准时间功能完整实现方案。

### 🎯 核心特点
- **最大化复用现有API**：直接使用current_time_ns()、thread_sleep_ns()、storage_open_session()等
- **最小化代码修改**：总代码量约400行，仅新增1个文件，修改3个现有文件
- **零破坏性修改**：100%兼容现有Trusty架构，不破坏任何现有功能
- **完整GP标准支持**：支持所有5个GP时间API和完整错误处理

## 📁 文件修改详细清单

### 1. 新增文件

#### **user/base/lib/libutee/tee_time.c** (新增，约350行)
```c
// 包含所有5个GP时间API的完整实现：
// - TEE_GetSystemTime()
// - TEE_GetTAPersistentTime() 
// - TEE_SetTAPersistentTime()
// - TEE_GetREETime()
// - TEE_Wait()
// 
// 以及所有内部函数：
// - get_current_ta_uuid()
// - tee_time_get_sys_time()
// - load_ta_time_offset()
// - save_ta_time_offset()
// - calculate_checksum()
// - calculate_ta_time()
// - calculate_offset()
```

### 2. 修改现有文件

#### **user/base/lib/libutee/include/tee_internal_api.h** (修改，约50行新增)
```c
// 新增内容：
// 1. TEE_Time结构体定义
// 2. GP标准错误码定义
// 3. 时间运算宏定义 (直接移植自OP-TEE)
// 4. GP时间API函数声明
// 5. 时间常量定义
```

#### **kernel/rctee/lib/rctee/rctee_core/syscall.c** (修改，约40行)
```c
// 修改内容：
// 1. 扩展现有sys_gettime()函数，添加时间类型参数支持
// 2. 新增sys_set_ta_persistent_time()函数
// 
// 修改位置：
// - 在现有sys_gettime()函数中添加switch语句处理不同时间类型
// - 在文件末尾添加新的系统调用函数
```

#### **kernel/rctee/lib/rctee/include/syscall_table.h** (修改，2行)
```c
// 修改内容：
// 1. 修改现有gettime系统调用定义，添加时间类型参数
// 2. 新增set_ta_persistent_time系统调用定义
```

#### **user/base/lib/libutee/tee_api_property.c** (修改，约20行)
```c
// 新增内容：
// 1. 时间保护级别属性定义数组
// 2. 属性注册函数
// 
// 修改位置：
// - 在现有属性定义后添加时间属性
// - 在初始化函数中调用时间属性注册
```

## 🔧 具体实施步骤

### 第一阶段：基础结构搭建 (1周)

#### **步骤1.1：创建头文件定义**
```bash
# 编辑 user/base/lib/libutee/include/tee_internal_api.h
# 添加以下内容：

typedef struct {
    uint32_t seconds;
    uint32_t millis;
} TEE_Time;

#define TEE_SUCCESS                    0x00000000
#define TEE_ERROR_TIME_NOT_SET         0xFFFF5000
#define TEE_ERROR_TIME_NEEDS_RESET     0xFFFF5001
#define TEE_ERROR_OVERFLOW             0xFFFF300F
// ... 其他错误码

#define TEE_TIME_MILLIS_BASE    1000
#define TEE_TIME_LT(t1, t2) \
    (((t1).seconds == (t2).seconds) ? \
        ((t1).millis < (t2).millis) : \
        ((t1).seconds < (t2).seconds))
// ... 其他时间运算宏

void TEE_GetSystemTime(TEE_Time* time);
TEE_Result TEE_GetTAPersistentTime(TEE_Time* time);
TEE_Result TEE_SetTAPersistentTime(const TEE_Time* time);
void TEE_GetREETime(TEE_Time* time);
TEE_Result TEE_Wait(uint32_t timeout);
```

#### **步骤1.2：创建GP时间API实现文件**
```bash
# 创建 user/base/lib/libutee/tee_time.c
# 实现所有GP时间API和内部函数
```

### 第二阶段：系统调用集成 (1周)

#### **步骤2.1：修改系统调用表**
```bash
# 编辑 kernel/rctee/lib/rctee/include/syscall_table.h
# 修改现有gettime系统调用定义：
DEF_SYSCALL(0x20, gettime, long, 3, uint32_t time_cat, uint32_t flags, user_addr_t time)

# 新增TA持久时间设置系统调用：
DEF_SYSCALL(0x61, set_ta_persistent_time, long, 1, user_addr_t time)
```

#### **步骤2.2：修改系统调用实现**
```bash
# 编辑 kernel/rctee/lib/rctee/rctee_core/syscall.c
# 修改现有sys_gettime()函数，添加时间类型参数处理
# 新增sys_set_ta_persistent_time()函数
```

### 第三阶段：属性系统集成 (0.5周)

#### **步骤3.1：添加时间保护级别属性**
```bash
# 编辑 user/base/lib/libutee/tee_api_property.c
# 添加时间保护级别属性定义和注册函数
```

### 第四阶段：测试验证 (0.5周)

#### **步骤4.1：功能测试**
```bash
# 创建测试TA验证以下功能：
# 1. TEE_GetSystemTime()基本功能
# 2. TEE_GetTAPersistentTime()状态管理
# 3. TEE_SetTAPersistentTime()偏移量存储
# 4. TEE_Wait()等待功能
# 5. 时间保护级别属性查询
```

## 🔍 关键实现细节

### 1. TA UUID获取机制
```c
// 使用线程名作为TA标识的简化实现
static TEE_Result get_current_ta_uuid(char* uuid_str, size_t size) {
    thread_t* current = get_current_thread();
    snprintf(uuid_str, size, "ta_%s", current->name);
    return TEE_SUCCESS;
}
```

### 2. 偏移量存储格式
```c
typedef struct {
    TEE_Time offset;     // 偏移量值
    bool positive;       // 偏移量符号
    uint32_t magic;      // 魔数验证 0x54414F46
    uint32_t checksum;   // 完整性校验
} ta_time_offset_t;
```

### 3. 时间计算核心算法
```c
// TA持久时间 = 系统时间 + 偏移量 (正偏移)
// TA持久时间 = 系统时间 - 偏移量 (负偏移)
if (offset->positive) {
    TEE_TIME_ADD(*sys_time, offset->offset, *ta_time);
} else {
    TEE_TIME_SUB(*sys_time, offset->offset, *ta_time);
}
```

### 4. 现有API复用策略
```c
// 直接调用现有Trusty API
lk_time_ns_t current_ns = current_time_ns();           // 系统时间获取
thread_sleep_ns(sleep_ns);                             // 等待实现
storage_open_session(&session, STORAGE_CLIENT_TD_PORT); // 存储操作
```

## ⚠️ 注意事项

### 1. 编译依赖
- 确保包含必要的头文件：`<tee_internal_api.h>`, `<syscall.h>`, `<rctee/storage.h>`
- 检查Makefile中是否包含新增的tee_time.c文件

### 2. 存储路径
- TA时间偏移量存储路径：`/ta_time_offs/{ta_uuid}.dat`
- 确保存储服务支持该路径的读写操作

### 3. 错误处理
- 所有GP API都必须正确处理和转换错误码
- 系统调用层需要将TEE_Result转换为系统调用返回值

### 4. 时间精度
- 系统时间基于current_time_ns()，精度为纳秒级
- GP时间精度为毫秒级，需要正确转换

## 📊 预期效果

### 1. 功能完整性
- ✅ 支持所有5个GP标准时间API
- ✅ 完整的错误码处理和状态管理
- ✅ 时间保护级别查询支持
- ✅ 偏移量机制的TA持久时间管理

### 2. 性能影响
- ✅ 最小化性能开销，直接复用现有API
- ✅ 存储操作仅在设置TA持久时间时发生
- ✅ 时间计算基于简单的加减运算

### 3. 兼容性保证
- ✅ 不破坏任何现有功能
- ✅ 系统调用向后兼容
- ✅ 完全复用现有基础设施

这个实施方案提供了完整、可行、风险可控的GP标准时间功能实现路径。
