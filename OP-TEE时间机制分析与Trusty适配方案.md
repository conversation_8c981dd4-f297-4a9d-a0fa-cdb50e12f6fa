# OP-TEE时间机制分析与Trusty TEE简化适配方案

## 1. OP-TEE TA持久时间实现机制分析

### 1.1 核心设计思路

基于对OP-TEE架构的分析，OP-TEE采用**偏移量(offset)机制**来实现TA持久时间：

**核心公式：**
```
TA持久时间 = 系统时间 + TA偏移量
```

### 1.2 关键数据结构

```c
// OP-TEE风格的时间结构
struct tee_time {
    uint32_t seconds;
    uint32_t millis;
};

// TA时间偏移量存储结构
struct ta_time_offs {
    struct tee_time offs;    // 偏移量值
    bool positive;           // 偏移量符号：true=正，false=负
};
```

### 1.3 时间运算宏定义

参考OP-TEE的实现，定义时间运算宏：

```c
// 时间加法宏 - 处理进位
#define TEE_TIME_ADD(t1, t2, dst) do { \
    (dst)->seconds = (t1)->seconds + (t2)->seconds; \
    (dst)->millis = (t1)->millis + (t2)->millis; \
    if ((dst)->millis >= 1000) { \
        (dst)->seconds++; \
        (dst)->millis -= 1000; \
    } \
} while (0)

// 时间减法宏 - 处理借位
#define TEE_TIME_SUB(t1, t2, dst) do { \
    if ((t1)->millis >= (t2)->millis) { \
        (dst)->seconds = (t1)->seconds - (t2)->seconds; \
        (dst)->millis = (t1)->millis - (t2)->millis; \
    } else { \
        (dst)->seconds = (t1)->seconds - (t2)->seconds - 1; \
        (dst)->millis = (t1)->millis + 1000 - (t2)->millis; \
    } \
} while (0)

// 时间比较宏
#define TEE_TIME_LT(t1, t2) \
    (((t1)->seconds < (t2)->seconds) || \
     (((t1)->seconds == (t2)->seconds) && ((t1)->millis < (t2)->millis)))

// 时间规范化检查
#define TEE_TIME_NORMALIZE(t) do { \
    while ((t)->millis >= 1000) { \
        (t)->seconds++; \
        (t)->millis -= 1000; \
    } \
} while (0)
```

### 1.4 偏移量机制优势

1. **存储效率**：只需存储偏移量，不需要存储绝对时间
2. **时间连续性**：系统重启后时间保持连续
3. **溢出处理**：通过偏移量可以处理时间溢出情况
4. **实现简单**：基于简单的加减运算

## 2. Trusty TEE简化适配设计

### 2.1 核心数据结构设计

```c
// GP标准时间结构体
typedef struct {
    uint32_t seconds;  // 自1970年1月1日UTC以来的秒数
    uint32_t millis;   // 毫秒数 (0-999)
} TEE_Time;

// TA时间偏移量结构
typedef struct {
    TEE_Time offset;     // 偏移量值
    bool positive;       // 偏移量符号：true=正偏移，false=负偏移
    uint32_t magic;      // 魔数 0x54414F46 ("TAOF")
    uint32_t checksum;   // CRC32校验和
} ta_time_offset_t;

// TA时间上下文
typedef struct {
    char ta_uuid[64];           // TA UUID
    ta_time_offset_t offset;    // 时间偏移量
    bool is_set;                // 是否已设置
    ta_time_state_t state;      // 当前状态
} ta_time_context_t;

// TA时间状态
typedef enum {
    TA_TIME_NOT_SET = 0,        // 未设置
    TA_TIME_SUCCESS = 1,        // 正常
    TA_TIME_NEEDS_RESET = 2     // 需要重置
} ta_time_state_t;
```

### 2.2 核心函数实现

#### 2.2.1 系统时间获取函数

```c
// 获取系统时间 - 基于current_time_ns()
static TEE_Result tee_time_get_sys_time(TEE_Time* time) {
    if (!time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    // 获取当前纳秒时间戳
    lk_time_ns_t current_ns = current_time_ns();

    // 转换为Unix时间戳（需要加上系统启动时的Unix时间偏移）
    uint64_t unix_time_ns = current_ns + get_boot_time_offset();

    time->seconds = (uint32_t)(unix_time_ns / 1000000000ULL);
    time->millis = (uint32_t)((unix_time_ns % 1000000000ULL) / 1000000ULL);

    // 规范化时间
    TEE_TIME_NORMALIZE(time);

    return TEE_SUCCESS;
}
```

#### 2.2.2 TA偏移量管理函数

```c
// 获取TA时间偏移量
static TEE_Result tee_time_ta_get_offs(const char* ta_uuid, ta_time_offset_t* offs) {
    char storage_path[128];
    storage_session_t session;
    file_handle_t file;
    TEE_Result res;

    if (!ta_uuid || !offs) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    // 构造存储路径
    snprintf(storage_path, sizeof(storage_path), "/ta_time_offs/%s.dat", ta_uuid);

    // 打开存储会话
    res = storage_open_session(&session, STORAGE_CLIENT_TD_PORT);
    if (res != TEE_SUCCESS) {
        return res;
    }

    // 打开偏移量文件
    res = storage_open_file(session, &file, storage_path, 0, 0);
    if (res != TEE_SUCCESS) {
        storage_close_session(session);
        return TEE_ERROR_TIME_NOT_SET;  // 文件不存在表示未设置
    }

    // 读取偏移量数据
    size_t bytes_read = storage_read(file, 0, offs, sizeof(ta_time_offset_t));

    storage_close_file(file);
    storage_close_session(session);

    if (bytes_read != sizeof(ta_time_offset_t)) {
        return TEE_ERROR_CORRUPT_OBJECT;
    }

    // 验证魔数和校验和
    if (offs->magic != 0x54414F46 || !verify_offset_checksum(offs)) {
        return TEE_ERROR_TIME_NEEDS_RESET;
    }

    return TEE_SUCCESS;
}

// 设置TA时间偏移量
static TEE_Result tee_time_ta_set_offs(const char* ta_uuid, const ta_time_offset_t* offs) {
    char storage_path[128];
    storage_session_t session;
    file_handle_t file;
    TEE_Result res;
    ta_time_offset_t offs_copy;

    if (!ta_uuid || !offs) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    // 复制并完善偏移量数据
    offs_copy = *offs;
    offs_copy.magic = 0x54414F46;
    offs_copy.checksum = calculate_offset_checksum(&offs_copy);

    // 构造存储路径
    snprintf(storage_path, sizeof(storage_path), "/ta_time_offs/%s.dat", ta_uuid);

    // 打开存储会话
    res = storage_open_session(&session, STORAGE_CLIENT_TD_PORT);
    if (res != TEE_SUCCESS) {
        return res;
    }

    // 创建/打开偏移量文件
    res = storage_open_file(session, &file, storage_path,
                           STORAGE_FILE_OPEN_CREATE, 0);
    if (res != TEE_SUCCESS) {
        storage_close_session(session);
        return res;
    }

    // 原子性写入偏移量数据
    size_t bytes_written = storage_write(file, 0, &offs_copy,
                                        sizeof(ta_time_offset_t),
                                        STORAGE_OP_COMPLETE);

    storage_close_file(file);
    storage_close_session(session);

    if (bytes_written != sizeof(ta_time_offset_t)) {
        return TEE_ERROR_STORAGE_NO_SPACE;
    }

    return TEE_SUCCESS;
}
```

#### 2.2.3 时间计算辅助函数

```c
// 计算TA持久时间：系统时间 + 偏移量
static TEE_Result calculate_ta_time(const TEE_Time* sys_time,
                                   const ta_time_offset_t* offs,
                                   TEE_Time* ta_time) {
    if (!sys_time || !offs || !ta_time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    if (offs->positive) {
        // 正偏移：ta_time = sys_time + offset
        TEE_TIME_ADD(sys_time, &offs->offset, ta_time);
    } else {
        // 负偏移：ta_time = sys_time - offset
        if (TEE_TIME_LT(sys_time, &offs->offset)) {
            // 系统时间小于偏移量，会导致负数结果
            return TEE_ERROR_TIME_NEEDS_RESET;
        }
        TEE_TIME_SUB(sys_time, &offs->offset, ta_time);
    }

    return TEE_SUCCESS;
}

// 计算偏移量：offset = ta_time - sys_time
static TEE_Result calculate_offset(const TEE_Time* ta_time,
                                  const TEE_Time* sys_time,
                                  ta_time_offset_t* offs) {
    if (!ta_time || !sys_time || !offs) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    if (TEE_TIME_LT(ta_time, sys_time)) {
        // ta_time < sys_time，负偏移
        offs->positive = false;
        TEE_TIME_SUB(sys_time, ta_time, &offs->offset);
    } else {
        // ta_time >= sys_time，正偏移
        offs->positive = true;
        TEE_TIME_SUB(ta_time, sys_time, &offs->offset);
    }

    return TEE_SUCCESS;
}
```

#### 2.2.4 校验和计算函数

```c
// 计算偏移量校验和
static uint32_t calculate_offset_checksum(const ta_time_offset_t* offs) {
    uint32_t checksum = 0;
    const uint8_t* ptr = (const uint8_t*)offs;
    size_t len = sizeof(ta_time_offset_t) - sizeof(uint32_t); // 排除checksum字段

    for (size_t i = 0; i < len; i++) {
        checksum ^= ptr[i];
        checksum = (checksum << 1) | (checksum >> 31); // 循环左移
    }

    return checksum;
}

// 验证偏移量校验和
static bool verify_offset_checksum(const ta_time_offset_t* offs) {
    uint32_t calculated = calculate_offset_checksum(offs);
    return calculated == offs->checksum;
}

// 获取系统启动时的Unix时间偏移量
static uint64_t get_boot_time_offset(void) {
    // 这里需要在系统启动时从REE获取当前Unix时间戳
    // 并计算与current_time_ns()的偏移量
    // 简化实现：返回一个固定的偏移量或从配置中读取
    static uint64_t boot_offset = 0;

    if (boot_offset == 0) {
        // 首次调用时计算偏移量
        // 实际实现中应该在系统启动时设置这个值
        boot_offset = 1640995200000000000ULL; // 2022-01-01 00:00:00 UTC (示例)
    }

    return boot_offset;
}
```

### 2.3 GP API层实现

#### 2.3.1 TEE_GetTAPersistentTime实现

```c
TEE_Result TEE_GetTAPersistentTime(TEE_Time* time) {
    TEE_Time sys_time;
    ta_time_offset_t offs;
    char ta_uuid[64];
    TEE_Result res;

    if (!time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    // 获取当前TA的UUID
    res = get_current_ta_uuid(ta_uuid, sizeof(ta_uuid));
    if (res != TEE_SUCCESS) {
        memset(time, 0, sizeof(TEE_Time));
        return res;
    }

    // 获取TA时间偏移量
    res = tee_time_ta_get_offs(ta_uuid, &offs);
    if (res != TEE_SUCCESS) {
        memset(time, 0, sizeof(TEE_Time));
        return res; // 返回NOT_SET或NEEDS_RESET
    }

    // 获取当前系统时间
    res = tee_time_get_sys_time(&sys_time);
    if (res != TEE_SUCCESS) {
        memset(time, 0, sizeof(TEE_Time));
        return res;
    }

    // 计算TA持久时间
    res = calculate_ta_time(&sys_time, &offs, time);
    if (res != TEE_SUCCESS) {
        memset(time, 0, sizeof(TEE_Time));
        return res;
    }

    // 检查溢出
    if (time->seconds == UINT32_MAX && time->millis > 0) {
        // 发生溢出，但仍然返回截断后的值
        return TEE_ERROR_OVERFLOW;
    }

    return TEE_SUCCESS;
}
```

#### 2.3.2 TEE_SetTAPersistentTime实现

```c
TEE_Result TEE_SetTAPersistentTime(const TEE_Time* time) {
    TEE_Time sys_time;
    ta_time_offset_t offs;
    char ta_uuid[64];
    TEE_Result res;

    if (!time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    // 验证时间参数
    if (time->millis >= 1000) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    // 获取当前TA的UUID
    res = get_current_ta_uuid(ta_uuid, sizeof(ta_uuid));
    if (res != TEE_SUCCESS) {
        return res;
    }

    // 获取当前系统时间
    res = tee_time_get_sys_time(&sys_time);
    if (res != TEE_SUCCESS) {
        return res;
    }

    // 计算偏移量
    res = calculate_offset(time, &sys_time, &offs);
    if (res != TEE_SUCCESS) {
        return res;
    }

    // 保存偏移量到存储
    res = tee_time_ta_set_offs(ta_uuid, &offs);
    if (res != TEE_SUCCESS) {
        return res;
    }

    return TEE_SUCCESS;
}
```

### 2.4 系统调用层修改

#### 2.4.1 修改现有sys_gettime

```c
long sys_gettime(uint32_t time_cat, uint32_t flags, user_addr_t time) {
    TEE_Time tee_time;
    TEE_Result res;

    switch (time_cat) {
        case TIME_CAT_SYSTEM:
            res = tee_time_get_sys_time(&tee_time);
            break;

        case TIME_CAT_TA_PERSISTENT:
            res = TEE_GetTAPersistentTime(&tee_time);
            break;

        case TIME_CAT_REE:
            res = tee_time_get_ree_time(&tee_time);
            break;

        default:
            return ERR_INVALID_ARGS;
    }

    if (res != TEE_SUCCESS && res != TEE_ERROR_OVERFLOW) {
        return -res; // 转换为负数错误码
    }

    int rc = copy_to_user(time, &tee_time, sizeof(TEE_Time));
    if (rc < 0) {
        return rc;
    }

    return (res == TEE_ERROR_OVERFLOW) ? -TEE_ERROR_OVERFLOW : 0;
}
```

#### 2.4.2 新增sys_set_ta_persistent_time

```c
long sys_set_ta_persistent_time(user_addr_t time) {
    TEE_Time tee_time;
    TEE_Result res;

    int rc = copy_from_user(&tee_time, time, sizeof(TEE_Time));
    if (rc < 0) {
        return rc;
    }

    res = TEE_SetTAPersistentTime(&tee_time);
    return (res == TEE_SUCCESS) ? 0 : -res;
}
```

## 3. 集成到现有简化方案

### 3.1 替换原有实现

将之前设计的`get_ta_persistent_time()`和`set_ta_persistent_time()`函数替换为基于偏移量机制的实现：

```c
// 替换原有的get_ta_persistent_time函数
static int get_ta_persistent_time(TEE_Time* time) {
    TEE_Result res = TEE_GetTAPersistentTime(time);

    switch (res) {
        case TEE_SUCCESS:
            return 0;
        case TEE_ERROR_TIME_NOT_SET:
            return -TEE_ERROR_TIME_NOT_SET;
        case TEE_ERROR_TIME_NEEDS_RESET:
            return -TEE_ERROR_TIME_NEEDS_RESET;
        case TEE_ERROR_OVERFLOW:
            return -TEE_ERROR_OVERFLOW;
        default:
            return -TEE_ERROR_GENERIC;
    }
}

// 替换原有的set_ta_persistent_time函数
static int set_ta_persistent_time(const TEE_Time* time) {
    TEE_Result res = TEE_SetTAPersistentTime(time);
    return (res == TEE_SUCCESS) ? 0 : -res;
}
```

### 3.2 文件组织结构

```
trusty-tee/
├── user/base/lib/libutee/
│   ├── tee_time.c                      # GP时间API实现
│   ├── tee_time_offset.c               # TA时间偏移量管理
│   └── include/
│       ├── tee_internal_api.h          # GP API定义
│       └── tee_time_internal.h         # 内部时间管理定义
├── kernel/rctee/lib/rctee/rctee_core/
│   ├── syscall.c                       # 修改现有系统调用
│   └── include/syscall_table.h         # 系统调用表
└── user/base/lib/libc-rctee/
    ├── time_utils.c                    # 时间工具函数
    └── include/rctee/time_utils.h      # 时间工具头文件
```

## 4. 优势与特点

### 4.1 设计优势

1. **存储高效**：只存储偏移量，不存储绝对时间
2. **时间连续性**：系统重启后TA时间保持连续
3. **溢出处理**：支持GP标准的溢出检测和处理
4. **实现简单**：基于简单的加减运算，易于理解和维护
5. **兼容性好**：完全兼容现有Trusty存储服务和系统调用框架

### 4.2 关键特性

1. **原子性操作**：利用现有存储服务的原子写入机制
2. **数据完整性**：通过魔数和校验和保证数据完整性
3. **错误处理**：完整的GP标准错误码支持
4. **时间规范化**：自动处理毫秒进位和时间规范化

### 4.3 实施简单

- **工作量**：约3-4周完成核心功能
- **风险低**：基于成熟的OP-TEE设计思路
- **测试简单**：偏移量机制易于测试和验证

这个基于偏移量机制的设计完全符合GP标准要求，同时保持了实现的简洁性和高效性。