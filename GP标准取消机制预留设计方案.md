# GP标准取消机制预留设计方案

## 1. OP-TEE取消机制深度分析

### 1.1 OP-TEE取消机制核心发现

通过分析OP-TEE的`tee_api.c`源码，发现以下关键取消机制实现：

#### **1.1.1 TEE_Wait取消支持**
```c
TEE_Result TEE_Wait(uint32_t timeout) {
    TEE_Result res = _utee_wait(timeout);

    if (res != TEE_SUCCESS && res != TEE_ERROR_CANCEL)
        TEE_Panic(res);

    return res;  // 可以返回TEE_ERROR_CANCEL
}
```

#### **1.1.2 取消标志管理API**
```c
bool TEE_GetCancellationFlag(void) {
    uint32_t c;
    TEE_Result res = _utee_get_cancellation_flag(&c);

    if (res != TEE_SUCCESS)
        c = 0;
    return !!c;
}

bool TEE_UnmaskCancellation(void) {
    uint32_t old_mask;
    TEE_Result res = _utee_unmask_cancellation(&old_mask);

    if (res != TEE_SUCCESS)
        TEE_Panic(res);
    return !!old_mask;
}

bool TEE_MaskCancellation(void) {
    uint32_t old_mask;
    TEE_Result res = _utee_mask_cancellation(&old_mask);

    if (res != TEE_SUCCESS)
        TEE_Panic(res);
    return !!old_mask;
}
```

#### **1.1.3 OP-TEE取消机制特点**
1. **取消标志**：通过`_utee_get_cancellation_flag()`检查取消状态
2. **取消掩码**：通过`_utee_mask_cancellation()`/`_utee_unmask_cancellation()`控制取消生效
3. **等待可取消**：`TEE_Wait()`可以返回`TEE_ERROR_CANCEL`
4. **系统调用支持**：底层系统调用支持取消检测和响应

## 2. Trusty TEE现状分析

### 2.1 现有sys_nanosleep实现

```c
long sys_nanosleep(uint32_t clock_id, uint32_t flags, uint64_t sleep_time) {
    thread_sleep_ns(sleep_time);  // 直接调用LK线程睡眠
    return NO_ERROR;
}
```

**关键发现：**
- `flags`参数目前未使用（`assert(flags == 0)`）
- 直接调用`thread_sleep_ns()`，无中断机制
- 无取消支持，无法提前唤醒

### 2.2 现有thread_sleep_ns实现分析

```c
void thread_sleep_ns(lk_time_ns_t delay_ns) {
    timer_t timer;
    thread_t *current_thread = get_current_thread();

    timer_initialize(&timer);
    // 设置定时器并阻塞线程
    // 无法被外部取消中断
}
```

**现状评估：**
- 基于定时器的睡眠机制
- 无外部中断或取消支持
- 线程状态管理完善（THREAD_BLOCKED等）

### 2.3 现有线程管理机制

```c
// 线程状态定义
enum thread_state {
    THREAD_SUSPENDED = 0,
    THREAD_READY,
    THREAD_RUNNING,
    THREAD_BLOCKED,
    THREAD_SLEEPING,
    THREAD_DEATH,
};

// 等待队列机制
typedef struct wait_queue {
    int magic;
    struct list_node list;
    int count;
} wait_queue_t;
```

**可用扩展点：**
- 完善的线程状态管理
- 等待队列机制（`wait_queue_block`）
- 线程标志位系统（`thread->flags`）

## 3. GP标准取消机制预留设计

### 3.1 flags参数扩展设计

在现有`sys_nanosleep()`的`flags`参数中预留取消相关标志位：

```c
/* 在 kernel/rctee/include/uapi/uapi/time.h 中定义 */

/* 现有flags定义（保持兼容） */
#define SLEEP_FLAG_NONE           0x00000000

/* 新增取消机制相关flags（预留） */
#define SLEEP_FLAG_CANCELLABLE    0x00000001  /* 支持取消的睡眠 */
#define SLEEP_FLAG_CHECK_CANCEL   0x00000002  /* 睡眠前检查取消标志 */
#define SLEEP_FLAG_MASK_CANCEL    0x00000004  /* 睡眠期间屏蔽取消 */

/* 预留未来扩展 */
#define SLEEP_FLAG_RESERVED_MASK  0xFFFFFFF8  /* 预留位，必须为0 */
```

### 3.2 线程取消状态数据结构

```c
/* 在 kernel/lk/include/kernel/thread.h 中添加 */

/* 线程取消状态 */
typedef struct thread_cancel_state {
    bool cancel_pending;      /* 是否有待处理的取消请求 */
    bool cancel_enabled;      /* 取消是否启用 */
    uint32_t cancel_mask;     /* 取消掩码计数 */
    uint32_t cancel_type;     /* 取消类型（预留） */
    void* cancel_cleanup;     /* 清理函数（预留） */
} thread_cancel_state_t;

/* 在thread_t结构体中添加取消状态字段 */
struct thread {
    // ... 现有字段 ...

    /* 取消机制支持（预留） */
    thread_cancel_state_t cancel_state;

    // ... 其他字段 ...
};
```

### 3.3 取消机制API预留接口

```c
/* 在 kernel/lk/include/kernel/thread.h 中添加函数声明 */

/* 线程取消管理接口（预留实现） */
status_t thread_set_cancel_pending(thread_t* t);
bool thread_is_cancel_pending(thread_t* t);
status_t thread_clear_cancel_pending(thread_t* t);

/* 取消掩码管理接口（预留实现） */
uint32_t thread_mask_cancellation(void);
uint32_t thread_unmask_cancellation(void);
bool thread_is_cancellation_masked(void);

/* 可取消等待接口（预留实现） */
status_t wait_queue_block_cancellable(wait_queue_t* wq, lk_time_t timeout, bool* cancelled);
void thread_sleep_ns_cancellable(lk_time_ns_t delay_ns, bool* cancelled);
```

### 3.4 系统调用层扩展预留

#### **3.4.1 扩展sys_nanosleep支持取消**

```c
/* 在 kernel/rctee/lib/rctee/rctee_core/syscall.c 中修改 */

long sys_nanosleep(uint32_t clock_id, uint32_t flags, uint64_t sleep_time) {
    /* 验证flags参数 */
    if (flags & SLEEP_FLAG_RESERVED_MASK) {
        return ERR_INVALID_ARGS;
    }

    /* 第一阶段：保持现有行为 */
    if (flags == SLEEP_FLAG_NONE) {
        thread_sleep_ns(sleep_time);
        return NO_ERROR;
    }

    /* 第二阶段：支持取消机制（预留实现） */
    if (flags & SLEEP_FLAG_CANCELLABLE) {
        bool cancelled = false;

        /* 睡眠前检查取消标志 */
        if ((flags & SLEEP_FLAG_CHECK_CANCEL) &&
            thread_is_cancel_pending(get_current_thread())) {
            return -TEE_ERROR_CANCEL;
        }

        /* 可取消睡眠 */
        thread_sleep_ns_cancellable(sleep_time, &cancelled);

        return cancelled ? -TEE_ERROR_CANCEL : NO_ERROR;
    }

    /* 默认行为 */
    thread_sleep_ns(sleep_time);
    return NO_ERROR;
}
```

#### **3.4.2 新增取消管理系统调用**

```c
/* 新增取消标志管理系统调用（预留） */
long sys_get_cancellation_flag(user_addr_t flag) {
    thread_t* current = get_current_thread();
    uint32_t cancel_flag = thread_is_cancel_pending(current) ? 1 : 0;

    return copy_to_user(flag, &cancel_flag, sizeof(uint32_t));
}

long sys_mask_cancellation(user_addr_t old_mask) {
    uint32_t prev_mask = thread_mask_cancellation();

    if (old_mask) {
        return copy_to_user(old_mask, &prev_mask, sizeof(uint32_t));
    }

    return NO_ERROR;
}

long sys_unmask_cancellation(user_addr_t old_mask) {
    uint32_t prev_mask = thread_unmask_cancellation();

    if (old_mask) {
        return copy_to_user(old_mask, &prev_mask, sizeof(uint32_t));
    }

    return NO_ERROR;
}
```

### 3.5 GP API层预留实现

```c
/* 在 user/base/lib/libutee/tee_time.c 中预留实现 */

/*
 * TEE_Wait - 支持取消的时间等待
 */
TEE_Result TEE_Wait(uint32_t timeout) {
    if (timeout == TEE_TIMEOUT_INFINITE) {
        timeout = UINT32_MAX - 1;
    }

    uint64_t sleep_ns = (uint64_t)timeout * 1000000ULL;

    /* 第一阶段：基础实现 */
    int rc = rctee_nanosleep(CLOCK_MONOTONIC, SLEEP_FLAG_NONE, sleep_ns);
    if (rc == 0) {
        return TEE_SUCCESS;
    }

    /* 第二阶段：支持取消机制（预留） */
    #ifdef SUPPORT_CANCELLATION
    rc = rctee_nanosleep(CLOCK_MONOTONIC,
                        SLEEP_FLAG_CANCELLABLE | SLEEP_FLAG_CHECK_CANCEL,
                        sleep_ns);

    switch (-rc) {
        case TEE_ERROR_CANCEL:
            return TEE_ERROR_CANCEL;
        case 0:
            return TEE_SUCCESS;
        default:
            return TEE_ERROR_GENERIC;
    }
    #endif

    return TEE_ERROR_GENERIC;
}

/* 取消标志管理API（预留实现） */
bool TEE_GetCancellationFlag(void) {
    #ifdef SUPPORT_CANCELLATION
    uint32_t flag;
    int rc = _rctee_get_cancellation_flag(&flag);
    return (rc == 0) ? !!flag : false;
    #else
    return false;  /* 第一阶段：总是返回false */
    #endif
}

bool TEE_UnmaskCancellation(void) {
    #ifdef SUPPORT_CANCELLATION
    uint32_t old_mask;
    int rc = _rctee_unmask_cancellation(&old_mask);
    return (rc == 0) ? !!old_mask : false;
    #else
    return false;  /* 第一阶段：总是返回false */
    #endif
}

bool TEE_MaskCancellation(void) {
    #ifdef SUPPORT_CANCELLATION
    uint32_t old_mask;
    int rc = _rctee_mask_cancellation(&old_mask);
    return (rc == 0) ? !!old_mask : false;
    #else
    return false;  /* 第一阶段：总是返回false */
    #endif
}
```

## 4. 渐进式实现路径规划

### 4.1 第一阶段：接口预留（立即实现）

#### **4.1.1 立即可实现的部分**
```c
/* 1. flags参数定义和验证 */
#define SLEEP_FLAG_CANCELLABLE    0x00000001
#define SLEEP_FLAG_CHECK_CANCEL   0x00000002
#define SLEEP_FLAG_MASK_CANCEL    0x00000004

/* 2. 系统调用参数验证 */
long sys_nanosleep(uint32_t clock_id, uint32_t flags, uint64_t sleep_time) {
    if (flags & SLEEP_FLAG_RESERVED_MASK) {
        return ERR_INVALID_ARGS;  /* 立即返回错误 */
    }

    if (flags != SLEEP_FLAG_NONE) {
        return ERR_NOT_SUPPORTED;  /* 暂不支持，但预留接口 */
    }

    /* 保持现有行为 */
    thread_sleep_ns(sleep_time);
    return NO_ERROR;
}

/* 3. GP API存根实现 */
bool TEE_GetCancellationFlag(void) {
    return false;  /* 第一阶段：总是返回false */
}

bool TEE_UnmaskCancellation(void) {
    return false;  /* 第一阶段：总是返回false */
}

bool TEE_MaskCancellation(void) {
    return false;  /* 第一阶段：总是返回false */
}
```

#### **4.1.2 数据结构预留**
```c
/* 在thread_t中预留取消状态字段 */
struct thread {
    // ... 现有字段 ...

    /* 取消机制支持（第一阶段：初始化为0） */
    uint32_t cancel_flags;     /* 简化的取消标志 */
    uint32_t reserved[3];      /* 为未来扩展预留 */

    // ... 其他字段 ...
};
```

### 4.2 第二阶段：核心功能实现（后续扩展）

#### **4.2.1 取消标志管理实现**
```c
/* 线程取消标志管理 */
status_t thread_set_cancel_pending(thread_t* t) {
    if (!t) return ERR_INVALID_ARGS;

    THREAD_LOCK(state);
    t->cancel_flags |= THREAD_CANCEL_PENDING;

    /* 如果线程正在睡眠且支持取消，唤醒它 */
    if ((t->state == THREAD_SLEEPING) &&
        (t->cancel_flags & THREAD_CANCEL_ENABLED)) {
        thread_unblock(t, true);
    }
    THREAD_UNLOCK(state);

    return NO_ERROR;
}

bool thread_is_cancel_pending(thread_t* t) {
    if (!t) return false;

    THREAD_LOCK(state);
    bool pending = !!(t->cancel_flags & THREAD_CANCEL_PENDING);
    THREAD_UNLOCK(state);

    return pending;
}
```

#### **4.2.2 可取消睡眠实现**
```c
/* 可取消的线程睡眠 */
void thread_sleep_ns_cancellable(lk_time_ns_t delay_ns, bool* cancelled) {
    timer_t timer;
    thread_t *current_thread = get_current_thread();

    if (cancelled) *cancelled = false;

    /* 设置取消支持标志 */
    THREAD_LOCK(state);
    current_thread->cancel_flags |= THREAD_CANCEL_ENABLED;

    /* 睡眠前检查取消标志 */
    if (current_thread->cancel_flags & THREAD_CANCEL_PENDING) {
        current_thread->cancel_flags &= ~THREAD_CANCEL_ENABLED;
        THREAD_UNLOCK(state);
        if (cancelled) *cancelled = true;
        return;
    }
    THREAD_UNLOCK(state);

    /* 设置定时器和等待 */
    timer_initialize(&timer);
    timer_set_oneshot_ns(&timer, delay_ns, thread_sleep_handler, current_thread);

    THREAD_LOCK(state);
    current_thread->state = THREAD_SLEEPING;
    thread_block();
    THREAD_UNLOCK(state);

    /* 检查唤醒原因 */
    THREAD_LOCK(state);
    bool was_cancelled = !!(current_thread->cancel_flags & THREAD_CANCEL_PENDING);
    current_thread->cancel_flags &= ~(THREAD_CANCEL_ENABLED | THREAD_CANCEL_PENDING);
    THREAD_UNLOCK(state);

    timer_cancel(&timer);

    if (cancelled) *cancelled = was_cancelled;
}
```

### 4.3 第三阶段：完整GP标准支持（最终目标）

#### **4.3.1 完整的取消掩码机制**
```c
/* 取消掩码管理 */
uint32_t thread_mask_cancellation(void) {
    thread_t* current = get_current_thread();

    THREAD_LOCK(state);
    uint32_t old_mask = current->cancel_flags & THREAD_CANCEL_MASKED;
    current->cancel_flags |= THREAD_CANCEL_MASKED;
    THREAD_UNLOCK(state);

    return old_mask ? 1 : 0;
}

uint32_t thread_unmask_cancellation(void) {
    thread_t* current = get_current_thread();

    THREAD_LOCK(state);
    uint32_t old_mask = current->cancel_flags & THREAD_CANCEL_MASKED;
    current->cancel_flags &= ~THREAD_CANCEL_MASKED;

    /* 如果有待处理的取消且未被屏蔽，立即处理 */
    if ((current->cancel_flags & THREAD_CANCEL_PENDING) && !old_mask) {
        /* 触发取消处理 */
    }
    THREAD_UNLOCK(state);

    return old_mask ? 1 : 0;
}
```

#### **4.3.2 完整的GP API实现**
```c
/* 完整的TEE_Wait实现 */
TEE_Result TEE_Wait(uint32_t timeout) {
    /* 检查取消掩码 */
    if (!thread_is_cancellation_masked() &&
        thread_is_cancel_pending(get_current_thread())) {
        return TEE_ERROR_CANCEL;
    }

    if (timeout == TEE_TIMEOUT_INFINITE) {
        timeout = UINT32_MAX - 1;
    }

    uint64_t sleep_ns = (uint64_t)timeout * 1000000ULL;

    /* 使用可取消睡眠 */
    int rc = rctee_nanosleep(CLOCK_MONOTONIC,
                            SLEEP_FLAG_CANCELLABLE | SLEEP_FLAG_CHECK_CANCEL,
                            sleep_ns);

    switch (-rc) {
        case TEE_ERROR_CANCEL:
            return TEE_ERROR_CANCEL;
        case 0:
            return TEE_SUCCESS;
        default:
            return TEE_ERROR_GENERIC;
    }
}
```

## 5. 实现优势和兼容性保证

### 5.1 完全向后兼容

1. **现有API不受影响**：所有现有的`rctee_nanosleep()`调用保持原有行为
2. **flags参数渐进扩展**：第一阶段只验证参数，不改变行为
3. **数据结构预留**：在thread_t中预留字段，不影响现有功能

### 5.2 渐进式实现优势

1. **风险可控**：分阶段实现，每阶段都可独立测试验证
2. **功能完整**：最终实现完整的GP标准取消机制
3. **实现灵活**：可根据需求优先级调整实现顺序

### 5.3 设计特点

1. **最小化修改**：第一阶段只需要很少的代码修改
2. **接口预留完整**：为完整的GP标准取消机制预留了所有必要接口
3. **架构兼容**：完全基于现有Trusty线程管理架构

这个预留设计方案既满足了GP标准的取消机制要求，又保持了与现有Trusty TEE架构的完全兼容性，为未来的功能扩展提供了清晰的实现路径。
